/**
 * MongoDB Animal Controller
 * 
 * This controller handles animal-related requests using MongoDB
 */

const AnimalModel = require('../../models/mongodb/animal.model');
const logger = require('../../utils/logger');

/**
 * Animal controller for MongoDB
 */
const animalController = {
  /**
   * Get all animals with pagination and filtering
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllAnimals: async (req, res) => {
    try {
      const {
        page,
        limit,
        species,
        status,
        location,
        gender,
        minWeight,
        maxWeight,
        search,
        sortBy,
        sortOrder
      } = req.query;

      const options = {
        page: parseInt(page) || 1,
        limit: parseInt(limit) || 10,
        species,
        status,
        location,
        gender,
        minWeight,
        maxWeight,
        search,
        sortBy,
        sortOrder
      };

      const result = await AnimalModel.findAll(options);

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error getting all animals from MongoDB:', error);
      res.status(500).json({
        error: {
          message: 'Error getting animals',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get animal by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAnimalById: async (req, res) => {
    try {
      const animal = await AnimalModel.findById(req.params.id);
      
      if (!animal) {
        return res.status(404).json({
          error: {
            message: 'Animal not found',
            status: 404
          }
        });
      }

      res.status(200).json({
        animal
      });
    } catch (error) {
      logger.error('Error getting animal by ID from MongoDB:', error);
      res.status(500).json({
        error: {
          message: 'Error getting animal',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get animal by tag number
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAnimalByTagNumber: async (req, res) => {
    try {
      const animal = await AnimalModel.findByTagNumber(req.params.tagNumber);
      
      if (!animal) {
        return res.status(404).json({
          error: {
            message: 'Animal not found',
            status: 404
          }
        });
      }

      res.status(200).json({
        animal
      });
    } catch (error) {
      logger.error('Error getting animal by tag number from MongoDB:', error);
      res.status(500).json({
        error: {
          message: 'Error getting animal',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Create a new animal
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createAnimal: async (req, res) => {
    try {
      const animalData = req.body;

      // Check if animal with tag number already exists
      const existingAnimal = await AnimalModel.findByTagNumber(animalData.tagNumber);

      if (existingAnimal) {
        return res.status(400).json({
          error: {
            message: 'Animal with this tag number already exists',
            status: 400
          }
        });
      }

      // Check BETA user animal limit
      if (req.user && req.user.role === 'beta') {
        const userAnimalsCount = await AnimalModel.countDocuments({
          createdBy: req.user.id
        });

        if (userAnimalsCount >= 50) {
          return res.status(403).json({
            error: {
              message: 'BETA users are limited to 50 animals. Upgrade to Professional for unlimited animals.',
              status: 403,
              code: 'BETA_LIMIT_EXCEEDED',
              limit: 50,
              current: userAnimalsCount
            }
          });
        }
      }

      // Add created by user ID if available
      if (req.user && req.user.id) {
        animalData.createdBy = req.user.id;
      }

      // Create animal
      const animal = await AnimalModel.create(animalData);

      res.status(201).json({
        message: 'Animal created successfully',
        animal
      });
    } catch (error) {
      logger.error('Error creating animal in MongoDB:', error);
      res.status(500).json({
        error: {
          message: 'Error creating animal',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Update animal
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateAnimal: async (req, res) => {
    try {
      const animalData = req.body;
      const id = req.params.id;

      // Check if animal exists
      const animal = await AnimalModel.findById(id);
      
      if (!animal) {
        return res.status(404).json({
          error: {
            message: 'Animal not found',
            status: 404
          }
        });
      }

      // Check if tag number is taken by another animal
      if (animalData.tagNumber && animalData.tagNumber !== animal.tagNumber) {
        const existingAnimal = await AnimalModel.findByTagNumber(animalData.tagNumber);
        
        if (existingAnimal && existingAnimal._id.toString() !== id) {
          return res.status(400).json({
            error: {
              message: 'Tag number already exists',
              status: 400
            }
          });
        }
      }

      // Add updated by user ID if available
      if (req.user && req.user.id) {
        animalData.updatedBy = req.user.id;
      }

      // Update animal
      const updatedAnimal = await AnimalModel.update(id, animalData);

      res.status(200).json({
        message: 'Animal updated successfully',
        animal: updatedAnimal
      });
    } catch (error) {
      logger.error('Error updating animal in MongoDB:', error);
      res.status(500).json({
        error: {
          message: 'Error updating animal',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Delete animal
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteAnimal: async (req, res) => {
    try {
      const id = req.params.id;

      // Check if animal exists
      const animal = await AnimalModel.findById(id);
      
      if (!animal) {
        return res.status(404).json({
          error: {
            message: 'Animal not found',
            status: 404
          }
        });
      }

      // Delete animal
      const deleted = await AnimalModel.delete(id);
      
      if (!deleted) {
        return res.status(500).json({
          error: {
            message: 'Failed to delete animal',
            status: 500
          }
        });
      }

      res.status(200).json({
        message: 'Animal deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting animal in MongoDB:', error);
      res.status(500).json({
        error: {
          message: 'Error deleting animal',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get animal statistics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAnimalStatistics: async (req, res) => {
    try {
      const statistics = await AnimalModel.getStatistics();

      res.status(200).json({
        statistics
      });
    } catch (error) {
      logger.error('Error getting animal statistics from MongoDB:', error);
      res.status(500).json({
        error: {
          message: 'Error getting animal statistics',
          details: error.message,
          status: 500
        }
      });
    }
  }
};

module.exports = animalController;
