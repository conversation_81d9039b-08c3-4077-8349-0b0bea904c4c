/**
 * New AgriIntel Landing Page Styles
 * Professional livestock management landing page with glassmorphism effects,
 * gradient themes, and accessibility compliance
 */

/* Import AgriIntel Branding */
@import url('./components/agriintel-branding.css');

/* CSS Variables for AgriIntel Brand Colors */
:root {
  /* Primary Brand Colors */
  --agri-deep-blue: #1565C0;
  --agri-emerald-green: #2E7D32;
  --agri-warm-gold: #F57C00;
  --agri-dull-green: #689F38;
  --agri-industrial-yellow: #FFA726;
  
  /* Extended Palette */
  --agri-forest-green: #1B5E20;
  --agri-ocean-blue: #0D47A1;
  --agri-sunset-orange: #FF8F00;
  --agri-earth-brown: #5D4037;
  --agri-sky-blue: #42A5F5;
  
  /* Neutral Colors */
  --agri-white: #FFFFFF;
  --agri-light-gray: #F5F5F5;
  --agri-medium-gray: #BDBDBD;
  --agri-dark-gray: #424242;
  --agri-black: #212121;
  
  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  /* Browser Compatibility */
  --webkit-backdrop-filter: blur(20px);
  --backdrop-filter: blur(20px);
  
  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Dark Mode Variables */
.dark-mode {
  --agri-white: #121212;
  --agri-light-gray: #1E1E1E;
  --agri-medium-gray: #424242;
  --agri-dark-gray: #E0E0E0;
  --agri-black: #FFFFFF;
  --glass-bg: rgba(0, 0, 0, 0.2);
  --glass-border: rgba(255, 255, 255, 0.1);
}

/* Base Styles */
.agriintel-landing {
  min-height: 100vh;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--agri-black);
  background: var(--agri-white);
  overflow-x: hidden;
  position: relative;
}

/* Background Container */
.landing-background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.landing-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(21, 101, 192, 0.85) 0%,
    rgba(46, 125, 50, 0.8) 25%,
    rgba(245, 124, 0, 0.75) 50%,
    rgba(104, 159, 56, 0.8) 75%,
    rgba(255, 167, 38, 0.85) 100%
  );
  -webkit-backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
  z-index: -1;
}

/* Navigation Styles */
.landing-navigation {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--glass-bg);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  padding: var(--spacing-md) 0;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.agriintel-logo-container {
  display: flex;
  align-items: center;
}

.logo-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.agriintel-logo-image {
  height: 80px;
  width: auto;
  max-width: 200px;
  object-fit: contain;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
  transition: all var(--transition-normal);
}

.agriintel-logo-image:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.2));
}

.logo-tagline {
  font-size: var(--font-size-xs) !important;
  color: var(--agri-white) !important;
  font-weight: 500 !important;
  margin-top: var(--spacing-xs) !important;
  text-align: center;
}

.nav-badge {
  background: linear-gradient(135deg, var(--agri-warm-gold), var(--agri-industrial-yellow)) !important;
  color: var(--agri-white) !important;
  font-weight: 600 !important;
  border-radius: var(--radius-lg) !important;
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.language-selector {
  min-width: 120px;
}

.language-selector .MuiSelect-select {
  color: var(--agri-white) !important;
  font-size: var(--font-size-sm) !important;
}

.language-selector .MuiOutlinedInput-notchedOutline {
  border-color: var(--glass-border) !important;
}

.language-selector:hover .MuiOutlinedInput-notchedOutline {
  border-color: var(--agri-warm-gold) !important;
}

.theme-toggle {
  color: var(--agri-white) !important;
  background: var(--glass-bg) !important;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border) !important;
  transition: all var(--transition-normal) !important;
}

.theme-toggle:hover {
  background: var(--agri-warm-gold) !important;
  transform: scale(1.05);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.nav-button {
  font-weight: 600 !important;
  text-transform: none !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-sm) var(--spacing-lg) !important;
  transition: all var(--transition-normal) !important;
  font-size: var(--font-size-sm) !important;
}

.nav-button-outline {
  color: var(--agri-white) !important;
  border-color: var(--glass-border) !important;
  background: var(--glass-bg) !important;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.nav-button-outline:hover {
  background: var(--agri-white) !important;
  color: var(--agri-deep-blue) !important;
  border-color: var(--agri-white) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

.nav-button-primary {
  background: linear-gradient(135deg, var(--agri-warm-gold), var(--agri-industrial-yellow)) !important;
  color: var(--agri-white) !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(245, 124, 0, 0.4) !important;
}

.nav-button-primary:hover {
  background: linear-gradient(135deg, var(--agri-industrial-yellow), var(--agri-warm-gold)) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 124, 0, 0.6) !important;
}

/* Hero Section */
.hero-section {
  padding: var(--spacing-3xl) 0;
  min-height: 90vh;
  display: flex;
  align-items: center;
  position: relative;
}

.hero-content {
  color: var(--agri-white);
  z-index: 2;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem) !important;
  font-weight: 800 !important;
  line-height: 1.1 !important;
  margin-bottom: var(--spacing-lg) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  max-width: 100%;
  word-wrap: break-word;
}

.gradient-text {
  background: linear-gradient(135deg, var(--agri-warm-gold), var(--agri-industrial-yellow), var(--agri-emerald-green));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.hero-subtitle {
  font-size: var(--font-size-xl) !important;
  font-weight: 400 !important;
  line-height: 1.6 !important;
  margin-bottom: var(--spacing-2xl) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  max-width: 600px;
}

.hero-stats {
  display: flex;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  background: var(--glass-bg);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  min-width: 120px;
}

.stat-number {
  font-size: var(--font-size-3xl) !important;
  font-weight: 700 !important;
  color: var(--agri-warm-gold) !important;
  margin-bottom: var(--spacing-xs) !important;
}

.stat-label {
  font-size: var(--font-size-sm) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 500 !important;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
  flex-wrap: wrap;
}

.cta-primary {
  background: linear-gradient(135deg, var(--agri-emerald-green), var(--agri-dull-green)) !important;
  color: var(--agri-white) !important;
  font-weight: 600 !important;
  font-size: var(--font-size-lg) !important;
  padding: var(--spacing-lg) var(--spacing-2xl) !important;
  border-radius: var(--radius-xl) !important;
  text-transform: none !important;
  box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4) !important;
  transition: all var(--transition-normal) !important;
}

.cta-primary:hover {
  background: linear-gradient(135deg, var(--agri-dull-green), var(--agri-emerald-green)) !important;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(46, 125, 50, 0.6) !important;
}

.cta-secondary {
  background: var(--glass-bg) !important;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  color: var(--agri-white) !important;
  border: 2px solid var(--glass-border) !important;
  font-weight: 600 !important;
  font-size: var(--font-size-lg) !important;
  padding: var(--spacing-lg) var(--spacing-2xl) !important;
  border-radius: var(--radius-xl) !important;
  text-transform: none !important;
  transition: all var(--transition-normal) !important;
}

.cta-secondary:hover {
  background: var(--agri-white) !important;
  color: var(--agri-deep-blue) !important;
  border-color: var(--agri-white) !important;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.hero-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: var(--glass-bg);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  width: fit-content;
}

.hero-rating .MuiRating-root {
  color: var(--agri-warm-gold) !important;
}

.hero-rating h6 {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
  margin: 0 !important;
}

.hero-visual {
  position: relative;
  z-index: 2;
}

.hero-dashboard-preview {
  position: relative;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  background: var(--glass-bg);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.hero-dashboard-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: var(--radius-2xl);
}

.dashboard-overlay {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--glass-bg);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-md);
}

.overlay-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--agri-white);
  font-weight: 600;
  font-size: var(--font-size-sm);
}

.overlay-badge .MuiSvgIcon-root {
  color: var(--agri-warm-gold);
}

/* Section Styles */
.features-section,
.pricing-section,
.testimonials-section,
.about-section,
.sponsors-section {
  padding: var(--spacing-3xl) 0;
  position: relative;
  z-index: 2;
}

.features-section {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(245, 245, 245, 0.9) 100%
  );
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.pricing-section {
  background: linear-gradient(
    135deg,
    rgba(21, 101, 192, 0.05) 0%,
    rgba(46, 125, 50, 0.05) 100%
  );
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

.testimonials-section {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(240, 240, 240, 0.85) 100%
  );
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.about-section {
  background: linear-gradient(
    135deg,
    rgba(46, 125, 50, 0.1) 0%,
    rgba(104, 159, 56, 0.1) 100%
  );
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

.sponsors-section {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(250, 250, 250, 0.9) 100%
  );
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem) !important;
  font-weight: 700 !important;
  color: var(--agri-black) !important;
  margin-bottom: var(--spacing-lg) !important;
  line-height: 1.2 !important;
  text-align: center;
  max-width: 100%;
}

.section-subtitle {
  font-size: var(--font-size-xl) !important;
  color: var(--agri-dark-gray) !important;
  font-weight: 400 !important;
  max-width: 600px;
  margin: 0 auto !important;
  line-height: 1.5 !important;
}

/* Feature Cards */
.feature-card {
  background: var(--glass-bg) !important;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--glass-shadow) !important;
  transition: all var(--transition-normal) !important;
  height: 100%;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
  border-color: var(--agri-warm-gold) !important;
}

.feature-icon-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

/* Feature Icon Gradients */
.feature-card:nth-child(1) .feature-icon-container {
  background: linear-gradient(135deg, var(--agri-emerald-green), var(--agri-dull-green));
}

.feature-card:nth-child(2) .feature-icon-container {
  background: linear-gradient(135deg, var(--agri-deep-blue), var(--agri-sky-blue));
}

.feature-card:nth-child(3) .feature-icon-container {
  background: linear-gradient(135deg, var(--agri-warm-gold), var(--agri-industrial-yellow));
}

.feature-card:nth-child(4) .feature-icon-container {
  background: linear-gradient(135deg, var(--agri-dull-green), var(--agri-emerald-green));
}

.feature-card:nth-child(5) .feature-icon-container {
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
}

.feature-card:nth-child(6) .feature-icon-container {
  background: linear-gradient(135deg, #F44336, #D32F2F);
}

.feature-icon {
  color: var(--agri-white) !important;
  font-size: 2rem !important;
}

.feature-title {
  font-size: var(--font-size-xl) !important;
  font-weight: 600 !important;
  color: var(--agri-black) !important;
  margin-bottom: var(--spacing-md) !important;
}

.feature-description {
  font-size: var(--font-size-base) !important;
  color: var(--agri-dark-gray) !important;
  line-height: 1.6 !important;
}

/* Pricing Cards */
.pricing-card {
  background: var(--glass-bg) !important;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 2px solid var(--glass-border) !important;
  border-radius: var(--radius-2xl) !important;
  box-shadow: var(--glass-shadow) !important;
  transition: all var(--transition-normal) !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: 100%;
}

.pricing-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2) !important;
}

.pricing-card-beta {
  border-color: var(--agri-industrial-yellow) !important;
}

.pricing-card-beta:hover {
  border-color: var(--agri-warm-gold) !important;
  box-shadow: 0 20px 50px rgba(255, 167, 38, 0.3) !important;
}

.pricing-card-professional {
  border-color: var(--agri-emerald-green) !important;
}

.pricing-card-professional:hover {
  border-color: var(--agri-dull-green) !important;
  box-shadow: 0 20px 50px rgba(46, 125, 50, 0.3) !important;
}

.pricing-card.popular {
  border-width: 3px !important;
  position: relative;
}

.pricing-card.popular::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--agri-emerald-green), var(--agri-warm-gold));
}

.plan-badge-container {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 10;
}

.plan-badge {
  font-weight: 700 !important;
  font-size: var(--font-size-xs) !important;
  padding: var(--spacing-xs) var(--spacing-md) !important;
  border-radius: var(--radius-lg) !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pricing-content {
  padding: var(--spacing-2xl) !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.plan-name {
  font-size: var(--font-size-3xl) !important;
  font-weight: 700 !important;
  color: var(--agri-black) !important;
  margin-bottom: var(--spacing-md) !important;
}

.plan-description {
  font-size: var(--font-size-base) !important;
  color: var(--agri-dark-gray) !important;
  margin-bottom: var(--spacing-xl) !important;
  line-height: 1.5 !important;
}

.plan-pricing {
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.plan-price {
  font-size: var(--font-size-5xl) !important;
  font-weight: 800 !important;
  color: var(--agri-black) !important;
  margin-bottom: var(--spacing-xs) !important;
}

.plan-period {
  font-size: var(--font-size-base) !important;
  color: var(--agri-dark-gray) !important;
  font-weight: 500 !important;
}

.plan-features {
  flex-grow: 1;
  margin-bottom: var(--spacing-xl);
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.feature-check {
  color: var(--agri-emerald-green) !important;
  font-size: 1.2rem !important;
  margin-top: 2px;
  flex-shrink: 0;
}

.feature-item .MuiTypography-root {
  font-size: var(--font-size-base) !important;
  color: var(--agri-black) !important;
  line-height: 1.4 !important;
}

.plan-limitations {
  background: rgba(255, 193, 7, 0.1);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.limitations-title {
  font-weight: 600 !important;
  color: var(--agri-warm-gold) !important;
  margin-bottom: var(--spacing-sm) !important;
}

.limitation-item {
  color: var(--agri-dark-gray) !important;
  display: block;
  margin-bottom: var(--spacing-xs) !important;
  line-height: 1.3 !important;
}

.plan-highlights {
  background: rgba(76, 175, 80, 0.1);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.highlight-item:last-child {
  margin-bottom: 0;
}

.highlight-icon {
  color: var(--agri-emerald-green) !important;
  font-size: 1rem !important;
}

.plan-cta {
  font-weight: 600 !important;
  font-size: var(--font-size-lg) !important;
  padding: var(--spacing-lg) !important;
  border-radius: var(--radius-xl) !important;
  text-transform: none !important;
  transition: all var(--transition-normal) !important;
}

.plan-cta-beta {
  background: linear-gradient(135deg, var(--agri-industrial-yellow), var(--agri-warm-gold)) !important;
  color: var(--agri-white) !important;
  box-shadow: 0 4px 15px rgba(255, 167, 38, 0.4) !important;
}

.plan-cta-beta:hover {
  background: linear-gradient(135deg, var(--agri-warm-gold), var(--agri-industrial-yellow)) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 167, 38, 0.6) !important;
}

.plan-cta-professional {
  background: linear-gradient(135deg, var(--agri-emerald-green), var(--agri-dull-green)) !important;
  color: var(--agri-white) !important;
  box-shadow: 0 4px 15px rgba(46, 125, 50, 0.4) !important;
}

.plan-cta-professional:hover {
  background: linear-gradient(135deg, var(--agri-dull-green), var(--agri-emerald-green)) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(46, 125, 50, 0.6) !important;
}

/* Testimonials */
.testimonial-card {
  background: var(--glass-bg) !important;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--glass-shadow) !important;
  transition: all var(--transition-normal) !important;
  height: 100%;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
  border-color: var(--agri-warm-gold) !important;
}

.testimonial-header {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.testimonial-avatar {
  width: 60px !important;
  height: 60px !important;
  border: 3px solid var(--agri-warm-gold) !important;
}

.testimonial-info {
  flex-grow: 1;
}

.testimonial-name {
  font-weight: 600 !important;
  color: var(--agri-black) !important;
  margin-bottom: var(--spacing-xs) !important;
}

.testimonial-role {
  color: var(--agri-dark-gray) !important;
  font-weight: 500 !important;
  margin-bottom: var(--spacing-xs) !important;
}

.testimonial-company {
  color: var(--agri-emerald-green) !important;
  font-weight: 600 !important;
  margin-bottom: var(--spacing-xs) !important;
}

.testimonial-location {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--agri-dark-gray) !important;
}

.testimonial-comment {
  font-style: italic !important;
  color: var(--agri-black) !important;
  line-height: 1.6 !important;
  margin-top: var(--spacing-md) !important;
}

/* About Section */
.about-title {
  font-size: var(--font-size-4xl) !important;
  font-weight: 700 !important;
  color: var(--agri-black) !important;
  margin-bottom: var(--spacing-md) !important;
}

.about-subtitle {
  font-size: var(--font-size-xl) !important;
  color: var(--agri-emerald-green) !important;
  font-weight: 600 !important;
  margin-bottom: var(--spacing-xl) !important;
}

.about-description {
  font-size: var(--font-size-lg) !important;
  color: var(--agri-dark-gray) !important;
  line-height: 1.7 !important;
  margin-bottom: var(--spacing-lg) !important;
}

.about-features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.about-feature {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: var(--glass-bg);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
}

.about-feature:hover {
  transform: translateX(10px);
  border-color: var(--agri-warm-gold);
}

.about-feature-icon {
  color: var(--agri-emerald-green) !important;
  font-size: 2rem !important;
}

.about-feature h6 {
  font-weight: 600 !important;
  color: var(--agri-black) !important;
  margin-bottom: var(--spacing-xs) !important;
}

.about-feature .MuiTypography-body2 {
  color: var(--agri-dark-gray) !important;
}

.about-image-container {
  position: relative;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.about-image {
  width: 100%;
  height: auto;
  display: block;
}

.about-overlay {
  position: absolute;
  bottom: var(--spacing-lg);
  left: var(--spacing-lg);
  background: var(--glass-bg);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  color: var(--agri-white);
  text-align: center;
}

.overlay-content h4 {
  font-weight: 700 !important;
  margin-bottom: var(--spacing-xs) !important;
  color: var(--agri-warm-gold) !important;
}

.overlay-content .MuiTypography-body2 {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Sponsors Section */
.sponsors-title {
  font-size: var(--font-size-2xl) !important;
  font-weight: 600 !important;
  color: var(--agri-black) !important;
  text-align: center !important;
  margin-bottom: var(--spacing-2xl) !important;
}

.sponsors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
  align-items: center;
  justify-items: center;
}

.sponsor-item {
  background: var(--agri-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-normal);
  width: 100%;
  max-width: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sponsor-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.sponsor-logo {
  max-width: 100%;
  max-height: 80px;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter var(--transition-normal);
}

.sponsor-item:hover .sponsor-logo {
  filter: grayscale(0%);
}

/* Footer */
.landing-footer {
  background: linear-gradient(
    135deg,
    rgba(33, 33, 33, 0.95) 0%,
    rgba(66, 66, 66, 0.9) 100%
  );
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  color: var(--agri-white);
  padding: var(--spacing-3xl) 0 var(--spacing-xl);
  position: relative;
  z-index: 2;
}

.footer-brand {
  margin-bottom: var(--spacing-xl);
}

.footer-logo {
  height: 50px;
  width: auto;
  margin-bottom: var(--spacing-lg);
}

.footer-description {
  color: rgba(255, 255, 255, 0.8) !important;
  line-height: 1.6 !important;
  margin-bottom: var(--spacing-lg) !important;
}

.footer-contact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: rgba(255, 255, 255, 0.7);
}

.contact-item .MuiSvgIcon-root {
  color: var(--agri-warm-gold);
}

.footer-section {
  margin-bottom: var(--spacing-xl);
}

.footer-title {
  font-weight: 600 !important;
  color: var(--agri-white) !important;
  margin-bottom: var(--spacing-lg) !important;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color var(--transition-normal);
  font-size: var(--font-size-sm);
}

.footer-links a:hover {
  color: var(--agri-warm-gold);
}

.footer-cta {
  margin-top: var(--spacing-md);
}

.footer-cta-button {
  background: linear-gradient(135deg, var(--agri-emerald-green), var(--agri-dull-green)) !important;
  color: var(--agri-white) !important;
  font-weight: 600 !important;
  text-transform: none !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-sm) var(--spacing-lg) !important;
  transition: all var(--transition-normal) !important;
}

.footer-cta-button:hover {
  background: linear-gradient(135deg, var(--agri-dull-green), var(--agri-emerald-green)) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(46, 125, 50, 0.4);
}

.footer-divider {
  background-color: rgba(255, 255, 255, 0.2) !important;
  margin: var(--spacing-xl) 0 !important;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.footer-copyright {
  color: rgba(255, 255, 255, 0.6) !important;
  font-size: var(--font-size-sm) !important;
}

.footer-badges {
  display: flex;
  gap: var(--spacing-sm);
}

.footer-badge {
  background: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  font-size: var(--font-size-xs) !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .agriintel-logo-image {
    height: 70px;
  }

  .hero-title {
    font-size: clamp(2rem, 4vw, 3.5rem) !important;
  }

  .section-title {
    font-size: clamp(1.75rem, 3.5vw, 2.5rem) !important;
  }

  .hero-stats {
    gap: var(--spacing-lg);
  }

  .tab-content-container {
    padding: var(--spacing-lg) 0;
  }
}

@media (max-width: 900px) {
  .nav-container {
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }

  .nav-controls {
    order: 3;
    width: 100%;
    justify-content: center;
  }

  .hero-title {
    font-size: var(--font-size-3xl) !important;
  }

  .hero-subtitle {
    font-size: var(--font-size-lg) !important;
  }

  .hero-stats {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
  }

  .stat-item {
    min-width: 200px;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-primary,
  .cta-secondary {
    width: 100%;
    max-width: 300px;
  }

  .sponsors-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
  }
}

@media (max-width: 600px) {
  .agriintel-logo-image {
    height: 40px;
  }

  .nav-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .nav-button {
    width: 100%;
    max-width: 200px;
  }

  .hero-section {
    padding: var(--spacing-2xl) 0;
  }

  .hero-title {
    font-size: var(--font-size-2xl) !important;
  }

  .hero-subtitle {
    font-size: var(--font-size-base) !important;
  }

  .section-title {
    font-size: var(--font-size-2xl) !important;
  }

  .section-subtitle {
    font-size: var(--font-size-base) !important;
  }

  .pricing-content {
    padding: var(--spacing-lg) !important;
  }

  .plan-name {
    font-size: var(--font-size-2xl) !important;
  }

  .plan-price {
    font-size: var(--font-size-3xl) !important;
  }

  .about-features {
    gap: var(--spacing-md);
  }

  .about-feature {
    flex-direction: column;
    text-align: center;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .sponsors-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States for Accessibility */
.nav-button:focus,
.cta-primary:focus,
.cta-secondary:focus,
.plan-cta:focus,
.footer-cta-button:focus {
  outline: 2px solid var(--agri-warm-gold);
  outline-offset: 2px;
}

.feature-card:focus,
.pricing-card:focus,
.testimonial-card:focus {
  outline: 2px solid var(--agri-warm-gold);
  outline-offset: 4px;
}

/* Tab Navigation Styles */
.tab-navigation-container {
  position: sticky;
  top: 145px;
  z-index: 999;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(240, 240, 240, 0.9) 100%
  );
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid var(--glass-border);
}

.tab-app-bar {
  background: transparent !important;
  box-shadow: none !important;
  color: var(--agri-black) !important;
}

.main-tabs {
  min-height: 64px;
}

.main-tabs .MuiTab-root {
  font-weight: 600 !important;
  font-size: var(--font-size-base) !important;
  text-transform: none !important;
  color: var(--agri-dark-gray) !important;
  min-height: 64px;
  padding: var(--spacing-lg) var(--spacing-xl) !important;
  transition: all var(--transition-normal) !important;
}

.main-tabs .MuiTab-root:hover {
  color: var(--agri-emerald-green) !important;
  background: rgba(46, 125, 50, 0.1);
}

.main-tabs .MuiTab-root.Mui-selected {
  color: var(--agri-emerald-green) !important;
  font-weight: 700 !important;
}

.main-tabs .MuiTabs-indicator {
  background: linear-gradient(90deg, var(--agri-emerald-green), var(--agri-warm-gold)) !important;
  height: 4px !important;
  border-radius: 2px;
}

.tab-content-container {
  min-height: calc(100vh - 200px);
  padding: var(--spacing-2xl) 0;
}

.tab-content-container .hero-section,
.tab-content-container .features-section,
.tab-content-container .pricing-section,
.tab-content-container .testimonials-section,
.tab-content-container .about-section {
  padding: var(--spacing-xl) 0;
}

/* Tab Panel Animations */
.MuiBox-root[role="tabpanel"] {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Tab Styles */
@media (max-width: 900px) {
  .tab-navigation-container {
    top: 120px;
  }

  .main-tabs .MuiTab-root {
    font-size: var(--font-size-sm) !important;
    padding: var(--spacing-md) var(--spacing-sm) !important;
    min-width: auto;
  }

  .tab-content-container {
    padding: var(--spacing-lg) 0;
  }
}

@media (max-width: 600px) {
  .main-tabs {
    min-height: 48px;
  }

  .main-tabs .MuiTab-root {
    min-height: 48px;
    font-size: var(--font-size-xs) !important;
    padding: var(--spacing-sm) var(--spacing-xs) !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .landing-background-container {
    display: none;
  }

  .agriintel-landing {
    background: var(--agri-white);
  }

  .background-overlay {
    display: none;
  }

  .hero-content,
  .nav-container {
    color: var(--agri-black) !important;
  }
}
